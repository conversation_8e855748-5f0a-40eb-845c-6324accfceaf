<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  getPreInvoicingList,
  updatePreInvoicing,
  preInvoicingBatchAction,
  exportPreInvoicing,
  getPreInvoicingDetail,
  deletePreInvoicingDetail,
} from "../../services/invoice";
import type {
  PreInvoicingItem,
  PreInvoicingSearchParams,
  PreInvoicingEditData,
  PreInvoicingBatchAction,
  PreInvoicingDetailItem,
  PreInvoicingDetailSearchParams,
} from "../../types/invoice";
import { getCustomersSimpleList } from "../../services/customer";
import { optionLoaders } from "../../utils/options";
import type { CustomerSimpleInfo } from "../../types/customer";
import { useToast } from "primevue/usetoast";
import { formatDateTime } from "../../utils/common";
import { InvoiceStatusMap, InvoiceStatusSeverityMap } from "../../utils/const";

const toast = useToast();
const loading = ref(false);
const items = ref<PreInvoicingItem[]>([]);
const totalRecords = ref(0);
const selectedItems = ref<PreInvoicingItem[]>([]);

// 筛选表单
const searchForm = ref<PreInvoicingSearchParams>({
  account_seq: "",
  create_user: "",
  customer_num: "",
  page: 1,
  pageSize: 20,
});

// 客户选项
const customerOptions = ref<CustomerSimpleInfo[]>([]);

// 编辑抽屉相关状态
const editDrawerVisible = ref(false);
const editLoading = ref(false);
const currentEditItem = ref<PreInvoicingItem | null>(null);

// 编辑表单数据
const editForm = ref<PreInvoicingEditData>({
  tax_rate: 0,
  invoice_currency_type: "",
  exchange_rate: "",
  remark: "",
});

// 表单验证错误状态
const fieldErrors = ref<Record<string, string>>({});

// 批量操作确认对话框
const batchActionDialogVisible = ref(false);
const batchActionLoading = ref(false);
const currentBatchAction = ref<"issuance" | "pause" | "cancel">("issuance");

// 导出功能状态
const exportLoading = ref(false);

// 行展开相关状态
const expandedRows = ref<Record<number, boolean>>({});
const detailData = ref<Record<number, PreInvoicingDetailItem[]>>({});
const detailLoading = ref<Record<number, boolean>>({});
const detailTotalRecords = ref<Record<number, number>>({});
const detailSearchParams = ref<Record<number, PreInvoicingDetailSearchParams>>({});

// 税率选项
const taxOptions = [
  { label: "0%", value: 0 },
  { label: "6%", value: 6 },
  { label: "9%", value: 9 },
  { label: "13%", value: 13 },
];

// 货币类型选项
const currencyOptions = ref<{ label: string; value: string }[]>([]);

// 错误处理回调函数
const handleOptionsError = (_error: any, message: string) => {
  toast.add({
    severity: "error",
    summary: "错误",
    detail: message,
    life: 3000,
  });
};

// 加载货币类型选项
const loadCurrencyOptions = () =>
  optionLoaders.currencyType(currencyOptions, handleOptionsError);

// 获取数据
const loadPreInvoicing = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getPreInvoicingList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadPreInvoicing();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    create_user: "",
    customer_num: "",
    page: 1,
    pageSize: 20,
  };
  loadPreInvoicing();
};

// 分页变化
const onPageChange = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadPreInvoicing();
};

// 加载客户列表
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    if (response.code === 200) {
      customerOptions.value = response.data;
    }
  } catch (error) {
    console.error("加载客户列表失败:", error);
  }
};

// 打开编辑抽屉
const openEditDrawer = (item: PreInvoicingItem) => {
  currentEditItem.value = item;
  editForm.value = {
    tax_rate: item.tax_rate,
    invoice_currency_type: item.invoice_currency_type,
    exchange_rate: item.exchange_rate.toString(),
    remark: item.remark,
  };
  fieldErrors.value = {};
  editDrawerVisible.value = true;
  loadCurrencyOptions();
};

// 关闭编辑抽屉
const closeEditDrawer = () => {
  editDrawerVisible.value = false;
  currentEditItem.value = null;
  fieldErrors.value = {};
  editForm.value = {
    tax_rate: 0,
    invoice_currency_type: "",
    exchange_rate: "",
    remark: "",
  };
};

// 表单验证
const validateEditForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "invoice_currency_type", label: "开票币种" },
    { key: "exchange_rate", label: "汇率" },
  ];

  requiredFields.forEach((field) => {
    const value = editForm.value[field.key as keyof PreInvoicingEditData];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "")
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  // 验证汇率必须大于0
  if (
    editForm.value.exchange_rate &&
    parseFloat(editForm.value.exchange_rate) <= 0
  ) {
    fieldErrors.value.exchange_rate = "汇率必须大于0";
    isValid = false;
  }

  if (!isValid) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
  }

  return isValid;
};

// 提交编辑
const submitEdit = async () => {
  if (!validateEditForm() || !currentEditItem.value) {
    return;
  }

  editLoading.value = true;
  try {
    const response = await updatePreInvoicing(
      currentEditItem.value.id,
      editForm.value
    );
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "更新成功",
        life: 3000,
      });
      closeEditDrawer();
      loadPreInvoicing();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "更新失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("更新失败:", error);
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "更新失败",
        life: 3000,
      });
    }
  } finally {
    editLoading.value = false;
  }
};

// 打开批量操作对话框
const openBatchActionDialog = (action: "issuance" | "pause" | "cancel") => {
  if (selectedItems.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择要操作的记录",
      life: 3000,
    });
    return;
  }
  currentBatchAction.value = action;
  batchActionDialogVisible.value = true;
};

// 关闭批量操作对话框
const closeBatchActionDialog = () => {
  batchActionDialogVisible.value = false;
  currentBatchAction.value = "issuance";
};

// 获取批量操作标题
const getBatchActionTitle = computed(() => {
  const actionMap = {
    issuance: "开票",
    pause: "暂不开票",
    cancel: "不开票",
  };
  return actionMap[currentBatchAction.value];
});

// 获取批量操作确认消息
const getBatchActionMessage = computed(() => {
  const actionMap = {
    issuance: "确认要对选中的记录执行开票操作吗？",
    pause: "确认要对选中的记录执行暂不开票操作吗？",
    cancel: "确认要对选中的记录执行不开票操作吗？",
  };
  return actionMap[currentBatchAction.value];
});

// 执行批量操作
const executeBatchAction = async () => {
  batchActionLoading.value = true;
  try {
    const requestData: PreInvoicingBatchAction = {
      invoice_ids: selectedItems.value.map((item) => item.id),
      action: currentBatchAction.value,
    };

    const response = await preInvoicingBatchAction(requestData);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: `${getBatchActionTitle.value}操作成功`,
        life: 3000,
      });
      closeBatchActionDialog();
      selectedItems.value = [];
      loadPreInvoicing();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || `${getBatchActionTitle.value}操作失败`,
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("批量操作失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail:
        error.response?.data?.message || `${getBatchActionTitle.value}操作失败`,
      life: 3000,
    });
  } finally {
    batchActionLoading.value = false;
  }
};

// 导出预开票信息
const handleExport = async () => {
  exportLoading.value = true;
  try {
    // 使用当前的筛选条件进行导出，但不包含分页参数
    const exportParams = {
      account_seq: searchForm.value.account_seq,
      create_user: searchForm.value.create_user,
      customer_num: searchForm.value.customer_num,
    };

    const blob = await exportPreInvoicing(exportParams);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;

    // 生成文件名，包含当前时间戳
    const timestamp = new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, "");
    link.download = `预开票信息_${timestamp}.xlsx`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放URL对象
    window.URL.revokeObjectURL(url);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "导出成功",
      life: 3000,
    });
  } catch (error: any) {
    console.error("导出失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "导出失败",
      life: 3000,
    });
  } finally {
    exportLoading.value = false;
  }
};

// 加载预开票详情数据
const loadPreInvoicingDetail = async (invoiceId: number, page = 1, pageSize = 10, searchQuery?: string) => {
  if (!detailSearchParams.value[invoiceId]) {
    detailSearchParams.value[invoiceId] = {
      page: 1,
      pageSize: 10
    };
  }

  detailSearchParams.value[invoiceId].page = page;
  detailSearchParams.value[invoiceId].pageSize = pageSize;

  // 根据invoice_pay_type设置不同的查询参数
  const currentItem = items.value.find(item => item.id === invoiceId);
  if (currentItem && searchQuery) {
    if (currentItem.invoice_pay_type === 1) {
      detailSearchParams.value[invoiceId].total_num = searchQuery;
      detailSearchParams.value[invoiceId].sub_order_no = undefined;
    } else {
      detailSearchParams.value[invoiceId].sub_order_no = searchQuery;
      detailSearchParams.value[invoiceId].total_num = undefined;
    }
  }

  detailLoading.value[invoiceId] = true;
  try {
    const response = await getPreInvoicingDetail(invoiceId, detailSearchParams.value[invoiceId]);
    if (response.code === 200) {
      detailData.value[invoiceId] = response.data.records;
      detailTotalRecords.value[invoiceId] = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取详情数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取详情数据失败",
      life: 3000,
    });
  } finally {
    detailLoading.value[invoiceId] = false;
  }
};

// 处理行展开
const onRowExpand = (event: any) => {
  const invoiceId = event.data.id;
  expandedRows.value[invoiceId] = true;
  loadPreInvoicingDetail(invoiceId);
};

// 处理行折叠
const onRowCollapse = (event: any) => {
  const invoiceId = event.data.id;
  expandedRows.value[invoiceId] = false;
};

// 处理详情分页变化
const onDetailPageChange = (invoiceId: number, event: any) => {
  loadPreInvoicingDetail(invoiceId, event.page + 1, event.rows);
};

// 处理详情搜索
const handleDetailSearch = (invoiceId: number, searchQuery: string) => {
  loadPreInvoicingDetail(invoiceId, 1, detailSearchParams.value[invoiceId]?.pageSize || 10, searchQuery);
};

// 删除预开票详情项
const deleteDetail = async (invoiceId: number, detailId: number) => {
  try {
    const response = await deletePreInvoicingDetail(invoiceId, detailId);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "删除成功",
        life: 3000,
      });
      // 重新加载详情数据
      loadPreInvoicingDetail(invoiceId, detailSearchParams.value[invoiceId]?.page, detailSearchParams.value[invoiceId]?.pageSize);
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "删除失败",
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "删除失败",
      life: 3000,
    });
  }
};

onMounted(() => {
  loadPreInvoicing();
  loadCustomerOptions();
});
</script>

<template>
  <!-- 搜索工具栏 -->
  <Toolbar class="mb-2">
    <template #start>
      <div class="flex items-center">
        <Message severity="info">预开票管理</Message>
      </div>
    </template>
    <template #end>
      <div class="flex flex-wrap gap-2 items-center">
        <Select
          v-model="searchForm.customer_num"
          :options="customerOptions"
          optionLabel="customer_name"
          optionValue="customer_num"
          class="w-48"
          showClear
          filter
          placeholder="选择客户"
        />
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">分账序号</label>
          <InputText v-model="searchForm.account_seq" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">创建者</label>
          <InputText v-model="searchForm.create_user" class="w-48" />
        </FloatLabel>
        <Button @click="handleSearch" icon="pi pi-search" rounded />
        <Button
          @click="handleReset"
          icon="pi pi-refresh"
          class="p-button-secondary"
          rounded
        />
      </div>
      <Divider layout="vertical" />
      <div class="flex gap-2">
        <Button
          icon="pi pi-check"
          label="开票"
          @click="openBatchActionDialog('issuance')"
          :disabled="selectedItems.length === 0"
          severity="success"
        />
        <Button
          icon="pi pi-pause"
          label="暂不开票"
          @click="openBatchActionDialog('pause')"
          :disabled="selectedItems.length === 0"
          severity="warn"
        />
        <Button
          icon="pi pi-ban"
          label="不开票"
          @click="openBatchActionDialog('cancel')"
          :disabled="selectedItems.length === 0"
          severity="danger"
        />
      </div>
      <Divider layout="vertical" />
      <Button
        icon="pi pi-file-export"
        @click="handleExport"
        :loading="exportLoading"
        severity="help"
      />
    </template>
  </Toolbar>

  <!-- 数据表格 -->
  <DataTable
    :value="items"
    v-model:selection="selectedItems"
    v-model:expandedRows="expandedRows"
    :loading="loading"
    :paginator="true"
    :rows="20"
    :totalRecords="totalRecords"
    :lazy="true"
    @page="onPageChange"
    @row-expand="onRowExpand"
    @row-collapse="onRowCollapse"
    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
    :rowsPerPageOptions="[20, 50, 100]"
    currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
    class="p-datatable-sm"
    showGridlines
    scrollable
    scrollHeight="calc(100vh - 25rem)"
    dataKey="id"
  >
    <template #empty>
      <div class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无预开票数据</p>
      </div>
    </template>

    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
    <Column :expander="true" headerStyle="width: 3rem"></Column>

    <Column field="invoice_no" header="发票编号" style="min-width: 16rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.invoice_no }}</span>
      </template>
    </Column>

    <Column field="customer_name" header="客户名称" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-medium">{{ data.customer_name }}</span>
      </template>
    </Column>

    <Column
      field="customer_invoice_name"
      header="开票名称"
      style="min-width: 12rem"
    >
      <template #body="{ data }">
        <span>{{ data.customer_invoice_name }}</span>
      </template>
    </Column>

    <Column field="account_seq" header="分账序号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.account_seq }}</span>
      </template>
    </Column>

    <Column field="amount" header="金额" style="min-width: 8rem">
      <template #body="{ data }">
        <span class="font-semibold text-blue-600">{{
          data.amount.toLocaleString()
        }}</span>
      </template>
    </Column>

    <Column field="tax_amount" header="税额" style="min-width: 8rem">
      <template #body="{ data }">
        <span class="font-semibold text-orange-600">{{
          data.tax_amount.toLocaleString()
        }}</span>
      </template>
    </Column>

    <Column field="invoice_type" header="发票类型" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.invoice_type" />
      </template>
    </Column>

    <Column field="tax_rate" header="税率" style="min-width: 6rem">
      <template #body="{ data }">
        <span>{{ data.tax_rate }}%</span>
      </template>
    </Column>

    <Column field="currency_type" header="币种" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag :value="data.currency_type" class="p-tag-info" />
      </template>
    </Column>

    <Column
      field="invoice_currency_type"
      header="开票币种"
      style="min-width: 6rem"
    >
      <template #body="{ data }">
        <Tag :value="data.invoice_currency_type" class="p-tag-secondary" />
      </template>
    </Column>

    <Column field="state" header="状态" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag
          :value="InvoiceStatusMap[data.state]"
          :severity="InvoiceStatusSeverityMap[data.state]"
        />
      </template>
    </Column>

    <Column field="create_user" header="创建者" style="min-width: 8rem">
      <template #body="{ data }">
        <span>{{ data.create_user }}</span>
      </template>
    </Column>

    <Column field="created_at" header="创建时间" style="min-width: 12rem">
      <template #body="{ data }">
        <span>{{ formatDateTime(data.created_at) }}</span>
      </template>
    </Column>

    <Column header="操作" style="min-width: 8rem" frozen alignFrozen="right">
      <template #body="{ data }">
        <Button
          icon="pi pi-pencil"
          @click="openEditDrawer(data)"
          outlined
          rounded
          class="mr-2"
        />
      </template>
    </Column>

    <!-- 行展开模板 -->
    <template #expansion="{ data }">
      <div class="p-4">
        <div class="flex justify-between items-center mb-4">
          <h5 class="font-semibold text-gray-700">预开票详情</h5>
          <div class="flex items-center gap-2">
            <InputText
              :placeholder="data.invoice_pay_type === 1 ? '搜索订单编号' : '搜索子订单编号'"
              @keyup.enter="(e: KeyboardEvent) => handleDetailSearch(data.id, (e.target as HTMLInputElement).value)"
              class="w-64"
            />
            <Button
              icon="pi pi-search"
              @click="(e) => {
                const input = (e.target as HTMLElement).parentElement?.querySelector('input') as HTMLInputElement;
                if (input) handleDetailSearch(data.id, input.value);
              }"
              outlined
              rounded
            />
          </div>
        </div>

        <DataTable
          :value="detailData[data.id] || []"
          :loading="detailLoading[data.id] || false"
          :paginator="true"
          :rows="10"
          :totalRecords="detailTotalRecords[data.id] || 0"
          :lazy="true"
          @page="(event) => onDetailPageChange(data.id, event)"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
          currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
          class="p-datatable-sm"
          showGridlines
        >
          <template #empty>
            <div class="text-center py-4 text-gray-500">
              暂无详情数据
            </div>
          </template>

          <!-- 根据invoice_pay_type显示不同的列 -->
          <Column
            v-if="data.invoice_pay_type !== 1"
            field="sub_order_no"
            header="子订单编号"
            style="min-width: 12rem"
          >
            <template #body="{ data: detailItem }">
              <span class="font-mono text-sm">{{ detailItem.sub_order_no || '-' }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type !== 1"
            field="charge_month"
            header="计费月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.charge_month || '-' }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type !== 1"
            field="adjust_month"
            header="调账月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.adjust_month || '-' }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type === 1"
            field="total_num"
            header="订单编号"
            style="min-width: 12rem"
          >
            <template #body="{ data: detailItem }">
              <span class="font-mono text-sm">{{ detailItem.total_num || '-' }}</span>
            </template>
          </Column>

          <Column field="target_type" header="目标类型" style="min-width: 8rem">
            <template #body="{ data: detailItem }">
              <Tag :value="detailItem.target_type" />
            </template>
          </Column>

          <Column field="amount" header="金额" style="min-width: 8rem">
            <template #body="{ data: detailItem }">
              <span class="font-semibold text-blue-600">{{ parseFloat(detailItem.amount).toLocaleString() }}</span>
            </template>
          </Column>

          <Column
            v-if="data.invoice_pay_type === 1"
            field="invoice_month"
            header="开票月份"
            style="min-width: 8rem"
          >
            <template #body="{ data: detailItem }">
              <span>{{ detailItem.invoice_month || '-' }}</span>
            </template>
          </Column>

          <Column field="created_at" header="创建时间" style="min-width: 12rem">
            <template #body="{ data: detailItem }">
              <span>{{ formatDateTime(detailItem.created_at) }}</span>
            </template>
          </Column>

          <Column header="操作" style="min-width: 6rem">
            <template #body="{ data: detailItem }">
              <Button
                icon="pi pi-trash"
                @click="deleteDetail(data.id, detailItem.id)"
                outlined
                rounded
                severity="danger"
                size="small"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </template>
  </DataTable>

  <!-- 编辑抽屉 -->
  <Drawer
    v-model:visible="editDrawerVisible"
    header="编辑预开票信息"
    position="right"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :style="{ width: '50rem' }"
  >
    <div class="p-4" v-if="currentEditItem">
      <!-- 基本信息（只读） -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <Fluid>
          <div class="grid grid-cols-2 gap-4">
            <div class="field">
              <label>发票编号</label>
              <InputText :value="currentEditItem.invoice_no" disabled />
            </div>
            <div class="field">
              <label>客户名称</label>
              <InputText :value="currentEditItem.customer_name" disabled />
            </div>
            <div class="field">
              <label>开票名称</label>
              <InputText
                :value="currentEditItem.customer_invoice_name"
                disabled
              />
            </div>
            <div class="field">
              <label>分账序号</label>
              <InputText :value="currentEditItem.account_seq" disabled />
            </div>
            <div class="field">
              <label>金额</label>
              <InputText
                :value="currentEditItem.amount.toLocaleString()"
                disabled
              />
            </div>
            <div class="field">
              <label>发票类型</label>
              <InputText :value="currentEditItem.invoice_type" disabled />
            </div>
            <div class="field">
              <label>币种类型</label>
              <InputText :value="currentEditItem.currency_type" disabled />
            </div>
            <div class="field">
              <label>签约主体</label>
              <InputText :value="currentEditItem.signing_entity" disabled />
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 可编辑信息 -->
      <div class="form-section">
        <h3 class="section-title">可编辑信息</h3>
        <Fluid>
          <div class="grid grid-cols-2 gap-4">
            <div class="field">
              <label class="required">税率</label>
              <Select
                v-model="editForm.tax_rate"
                :options="taxOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="选择税率"
                class="w-full"
              />
            </div>
            <div class="field">
              <label class="required">开票币种</label>
              <Select
                v-model="editForm.invoice_currency_type"
                :options="currencyOptions"
                optionLabel="label"
                optionValue="value"
                showClear
                filter
                placeholder="选择开票币种"
                class="w-full"
                :class="{ 'p-invalid': fieldErrors.invoice_currency_type }"
              />
              <small v-if="fieldErrors.invoice_currency_type" class="p-error">
                {{ fieldErrors.invoice_currency_type }}
              </small>
            </div>
            <div class="field col-span-2">
              <label class="required">汇率</label>
              <InputNumber
                :model-value="parseFloat(editForm.exchange_rate || '0')"
                @update:model-value="
                  (value) => {
                    editForm.exchange_rate = value?.toString() || '0';
                  }
                "
                :min="0"
                :minFractionDigits="0"
                :maxFractionDigits="10"
                placeholder="输入汇率"
                class="w-full"
                :class="{ 'p-invalid': fieldErrors.exchange_rate }"
              />
              <small v-if="fieldErrors.exchange_rate" class="p-error">
                {{ fieldErrors.exchange_rate }}
              </small>
            </div>
            <div class="field col-span-2">
              <label>备注</label>
              <Textarea
                v-model="editForm.remark"
                rows="3"
                placeholder="输入备注信息"
                class="w-full"
              />
            </div>
          </div>
        </Fluid>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          @click="closeEditDrawer"
          class="p-button-secondary"
        />
        <Button
          label="保存"
          icon="pi pi-check"
          @click="submitEdit"
          :loading="editLoading"
          class="p-button-success"
        />
      </div>
    </template>
  </Drawer>

  <!-- 批量操作确认对话框 -->
  <Dialog
    v-model:visible="batchActionDialogVisible"
    modal
    :header="`确认${getBatchActionTitle}`"
    :style="{ width: '400px' }"
    @hide="closeBatchActionDialog"
    :draggable="false"
    :resizable="false"
  >
    <div class="confirmation-content">
      <div class="flex items-center gap-3 mb-4">
        <i class="pi pi-exclamation-triangle text-orange-500"></i>
        <span>{{ getBatchActionMessage }}</span>
      </div>
      <div class="text-gray-600 ml-7">
        已选择 <strong>{{ selectedItems.length }}</strong> 条记录
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          @click="closeBatchActionDialog"
          class="p-button-secondary"
        />
        <Button
          :label="getBatchActionTitle"
          icon="pi pi-check"
          @click="executeBatchAction"
          :loading="batchActionLoading"
          :class="{
            'p-button-success': currentBatchAction === 'issuance',
            'p-button-warn': currentBatchAction === 'pause',
            'p-button-danger': currentBatchAction === 'cancel',
          }"
        />
      </div>
    </template>
  </Dialog>

  <Toast />
</template>

<style scoped>
/* 工具栏和表格样式 */
:deep(.p-toolbar) {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f1f5f9;
  color: #475569;
  font-weight: 600;
  border-color: #e2e8f0;
}

:deep(.p-datatable .p-datatable-tbody > tr:nth-child(odd)) {
  background: #f8fafc;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  background: #e0f2fe;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

/* 确认对话框样式 */
.confirmation-content {
  padding: 1rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
